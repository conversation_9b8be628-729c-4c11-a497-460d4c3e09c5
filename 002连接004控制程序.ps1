# 002连接004控制程序
# 版本: v1.0 | 创建时间: 2025-07-23
# 功能: 002电脑连接控制004电脑
# 目标IP: ************** (004电脑) - 已更新正确IP
# 用户配置: administrator (系统) / apple (桌面)

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 全局变量
$global:session = $null
$global:connected = $false
$global:currentUser = "administrator"

# 创建主窗体
$form = New-Object System.Windows.Forms.Form
$form.Text = "002连接004控制程序 - 远程控制中心"
$form.Size = New-Object System.Drawing.Size(850, 650)
$form.StartPosition = "CenterScreen"
$form.BackColor = [System.Drawing.Color]::FromArgb(240, 248, 255)
$form.FormBorderStyle = "FixedSingle"
$form.MaximizeBox = $false

# 标题标签
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "🖥️ 002电脑 (PC-002) → 004电脑 控制中心"
$titleLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 16, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::FromArgb(25, 25, 112)
$titleLabel.Location = New-Object System.Drawing.Point(20, 20)
$titleLabel.Size = New-Object System.Drawing.Size(800, 40)
$titleLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($titleLabel)

# 用户选择面板
$userPanel = New-Object System.Windows.Forms.Panel
$userPanel.Location = New-Object System.Drawing.Point(20, 70)
$userPanel.Size = New-Object System.Drawing.Size(800, 50)
$userPanel.BackColor = [System.Drawing.Color]::FromArgb(255, 248, 220)
$userPanel.BorderStyle = "FixedSingle"
$form.Controls.Add($userPanel)

# 用户选择标签
$userLabel = New-Object System.Windows.Forms.Label
$userLabel.Text = "👤 选择004电脑用户:"
$userLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10, [System.Drawing.FontStyle]::Bold)
$userLabel.ForeColor = [System.Drawing.Color]::FromArgb(184, 134, 11)
$userLabel.Location = New-Object System.Drawing.Point(20, 15)
$userLabel.Size = New-Object System.Drawing.Size(150, 20)
$userPanel.Controls.Add($userLabel)

# 系统用户单选按钮
$adminRadio = New-Object System.Windows.Forms.RadioButton
$adminRadio.Text = "🔧 administrator (系统用户)"
$adminRadio.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$adminRadio.Location = New-Object System.Drawing.Point(180, 15)
$adminRadio.Size = New-Object System.Drawing.Size(200, 20)
$adminRadio.Checked = $true
$userPanel.Controls.Add($adminRadio)

# 桌面用户单选按钮
$appleRadio = New-Object System.Windows.Forms.RadioButton
$appleRadio.Text = "🍎 apple (桌面用户)"
$appleRadio.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$appleRadio.Location = New-Object System.Drawing.Point(400, 15)
$appleRadio.Size = New-Object System.Drawing.Size(180, 20)
$userPanel.Controls.Add($appleRadio)

# 连接状态面板
$statusPanel = New-Object System.Windows.Forms.Panel
$statusPanel.Location = New-Object System.Drawing.Point(20, 130)
$statusPanel.Size = New-Object System.Drawing.Size(800, 80)
$statusPanel.BackColor = [System.Drawing.Color]::FromArgb(245, 245, 245)
$statusPanel.BorderStyle = "FixedSingle"
$form.Controls.Add($statusPanel)

# 连接状态标签
$statusLabel = New-Object System.Windows.Forms.Label
$statusLabel.Text = "🔴 未连接到004电脑"
$statusLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 12, [System.Drawing.FontStyle]::Bold)
$statusLabel.ForeColor = [System.Drawing.Color]::Red
$statusLabel.Location = New-Object System.Drawing.Point(20, 15)
$statusLabel.Size = New-Object System.Drawing.Size(350, 25)
$statusPanel.Controls.Add($statusLabel)

# 004电脑信息标签
$infoLabel = New-Object System.Windows.Forms.Label
$infoLabel.Text = "目标: 004电脑 (**************) | 用户: administrator | 密码: (空)"
$infoLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$infoLabel.ForeColor = [System.Drawing.Color]::FromArgb(70, 70, 70)
$infoLabel.Location = New-Object System.Drawing.Point(20, 45)
$infoLabel.Size = New-Object System.Drawing.Size(550, 20)
$statusPanel.Controls.Add($infoLabel)

# 连接按钮
$connectButton = New-Object System.Windows.Forms.Button
$connectButton.Text = "🔗 连接004电脑"
$connectButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 11, [System.Drawing.FontStyle]::Bold)
$connectButton.Location = New-Object System.Drawing.Point(600, 20)
$connectButton.Size = New-Object System.Drawing.Size(150, 40)
$connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 123, 255)
$connectButton.ForeColor = [System.Drawing.Color]::White
$connectButton.FlatStyle = "Flat"
$statusPanel.Controls.Add($connectButton)

# 功能按钮面板
$buttonPanel = New-Object System.Windows.Forms.Panel
$buttonPanel.Location = New-Object System.Drawing.Point(20, 230)
$buttonPanel.Size = New-Object System.Drawing.Size(800, 200)
$buttonPanel.BackColor = [System.Drawing.Color]::FromArgb(248, 249, 250)
$buttonPanel.BorderStyle = "FixedSingle"
$form.Controls.Add($buttonPanel)

# 功能按钮标题
$buttonTitle = New-Object System.Windows.Forms.Label
$buttonTitle.Text = "🎮 004电脑控制功能"
$buttonTitle.Font = New-Object System.Drawing.Font("Microsoft YaHei", 12, [System.Drawing.FontStyle]::Bold)
$buttonTitle.ForeColor = [System.Drawing.Color]::FromArgb(25, 25, 112)
$buttonTitle.Location = New-Object System.Drawing.Point(20, 10)
$buttonTitle.Size = New-Object System.Drawing.Size(200, 25)
$buttonPanel.Controls.Add($buttonTitle)

# 创建功能按钮
$buttons = @(
    @{Text="📊 系统状态"; Command="SystemStatus"; X=20; Y=50},
    @{Text="📁 文件管理"; Command="FileManager"; X=200; Y=50},
    @{Text="🧹 清理缓存"; Command="CleanCache"; X=380; Y=50},
    @{Text="💻 VSCode修复"; Command="VSCodeFix"; X=560; Y=50},
    @{Text="🔧 服务管理"; Command="ServiceManager"; X=20; Y=100},
    @{Text="🌐 网络测试"; Command="NetworkTest"; X=200; Y=100},
    @{Text="👤 用户管理"; Command="UserManager"; X=380; Y=100},
    @{Text="🔄 重启004"; Command="Restart"; X=560; Y=100},
    @{Text="📋 获取信息"; Command="GetInfo"; X=20; Y=150},
    @{Text="🍎 Apple桌面"; Command="AppleDesktop"; X=200; Y=150},
    @{Text="🔐 权限检查"; Command="PermCheck"; X=380; Y=150},
    @{Text="📱 双用户状态"; Command="DualUser"; X=560; Y=150}
)

foreach ($btn in $buttons) {
    $button = New-Object System.Windows.Forms.Button
    $button.Text = $btn.Text
    $button.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
    $button.Location = New-Object System.Drawing.Point($btn.X, $btn.Y)
    $button.Size = New-Object System.Drawing.Size(160, 35)
    $button.BackColor = [System.Drawing.Color]::FromArgb(108, 117, 125)
    $button.ForeColor = [System.Drawing.Color]::White
    $button.FlatStyle = "Flat"
    $button.Enabled = $false
    $button.Tag = $btn.Command
    $buttonPanel.Controls.Add($button)
}

# 输出文本框
$outputBox = New-Object System.Windows.Forms.TextBox
$outputBox.Location = New-Object System.Drawing.Point(20, 450)
$outputBox.Size = New-Object System.Drawing.Size(800, 150)
$outputBox.Multiline = $true
$outputBox.ScrollBars = "Vertical"
$outputBox.Font = New-Object System.Drawing.Font("Consolas", 9)
$outputBox.BackColor = [System.Drawing.Color]::FromArgb(33, 37, 41)
$outputBox.ForeColor = [System.Drawing.Color]::FromArgb(248, 249, 250)
$outputBox.ReadOnly = $true
$form.Controls.Add($outputBox)

# 输出标签
$outputLabel = New-Object System.Windows.Forms.Label
$outputLabel.Text = "📝 操作日志"
$outputLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10, [System.Drawing.FontStyle]::Bold)
$outputLabel.ForeColor = [System.Drawing.Color]::FromArgb(25, 25, 112)
$outputLabel.Location = New-Object System.Drawing.Point(20, 425)
$outputLabel.Size = New-Object System.Drawing.Size(100, 20)
$form.Controls.Add($outputLabel)

# 清空日志按钮
$clearButton = New-Object System.Windows.Forms.Button
$clearButton.Text = "🗑️ 清空日志"
$clearButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 8)
$clearButton.Location = New-Object System.Drawing.Point(730, 420)
$clearButton.Size = New-Object System.Drawing.Size(90, 25)
$clearButton.BackColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
$clearButton.ForeColor = [System.Drawing.Color]::White
$clearButton.FlatStyle = "Flat"
$form.Controls.Add($clearButton)

# 函数：输出日志
function Write-Log {
    param($message, $color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $logMessage = "[$timestamp] $message`r`n"
    $outputBox.AppendText($logMessage)
    $outputBox.SelectionStart = $outputBox.Text.Length
    $outputBox.ScrollToCaret()
}

# 函数：更新用户信息
function Update-UserInfo {
    if ($adminRadio.Checked) {
        $global:currentUser = "administrator"
        $infoLabel.Text = "目标: 004电脑 (**************) | 用户: administrator (系统) | 密码: (空)"
    } else {
        $global:currentUser = "apple"
        $infoLabel.Text = "目标: 004电脑 (**************) | 用户: apple (桌面) | 密码: (空)"
    }
}

# 用户选择事件
$adminRadio.Add_CheckedChanged({ Update-UserInfo })
$appleRadio.Add_CheckedChanged({ Update-UserInfo })

# 函数：连接004电脑
function Connect-To004 {
    Write-Log "正在连接到004电脑 (用户: $global:currentUser)..." "Yellow"
    
    try {
        # 配置WinRM
        Set-Item WSMan:\localhost\Client\TrustedHosts -Value '*' -Force
        
        # 测试连接
        if (-not (Test-Connection -ComputerName "**************" -Count 1 -Quiet)) {
            throw "网络连接失败 - 004电脑可能未开机或网络不通"
        }
        
        # 创建凭据
        $securePassword = New-Object System.Security.SecureString
        $cred = New-Object PSCredential($global:currentUser, $securePassword)
        
        # 建立会话
        $global:session = New-PSSession -ComputerName "**************" -Credential $cred
        
        # 获取004电脑信息
        $info = Invoke-Command -Session $global:session -ScriptBlock {
            @{
                Hostname = hostname
                User = whoami
                OS = (Get-WmiObject Win32_OperatingSystem).Caption
                Memory = [math]::Round((Get-WmiObject Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)
                FreeSpace = [math]::Round((Get-WmiObject Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB, 2)
                Desktop = [Environment]::GetFolderPath('Desktop')
                Users = (Get-WmiObject Win32_UserAccount -Filter "LocalAccount=True").Name -join ", "
            }
        }
        
        $global:connected = $true
        $statusLabel.Text = "🟢 已连接到004电脑 (用户: $global:currentUser)"
        $statusLabel.ForeColor = [System.Drawing.Color]::Green
        $connectButton.Text = "🔌 断开连接"
        $connectButton.BackColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
        
        # 启用功能按钮
        foreach ($control in $buttonPanel.Controls) {
            if ($control -is [System.Windows.Forms.Button] -and $control.Tag) {
                $control.Enabled = $true
                $control.BackColor = [System.Drawing.Color]::FromArgb(40, 167, 69)
            }
        }
        
        Write-Log "✅ 成功连接到004电脑" "Green"
        Write-Log "主机名: $($info.Hostname)" "Cyan"
        Write-Log "当前用户: $($info.User)" "Cyan"
        Write-Log "系统: $($info.OS)" "Cyan"
        Write-Log "内存: $($info.Memory) GB" "Cyan"
        Write-Log "C盘可用空间: $($info.FreeSpace) GB" "Cyan"
        Write-Log "桌面路径: $($info.Desktop)" "Cyan"
        Write-Log "本地用户: $($info.Users)" "Cyan"
        
    } catch {
        Write-Log "❌ 连接失败: $($_.Exception.Message)" "Red"
        [System.Windows.Forms.MessageBox]::Show("连接004电脑失败:`n$($_.Exception.Message)`n`n请确保:`n1. 004电脑已开机`n2. 004电脑网络正常`n3. 004电脑已启用远程管理`n4. 选择的用户账户正确", "连接错误", "OK", "Error")
    }
}

# 函数：断开连接
function Disconnect-From004 {
    if ($global:session) {
        Remove-PSSession $global:session
        $global:session = $null
    }
    
    $global:connected = $false
    $statusLabel.Text = "🔴 未连接到004电脑"
    $statusLabel.ForeColor = [System.Drawing.Color]::Red
    $connectButton.Text = "🔗 连接004电脑"
    $connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 123, 255)
    
    # 禁用功能按钮
    foreach ($control in $buttonPanel.Controls) {
        if ($control -is [System.Windows.Forms.Button] -and $control.Tag) {
            $control.Enabled = $false
            $control.BackColor = [System.Drawing.Color]::FromArgb(108, 117, 125)
        }
    }
    
    Write-Log "🔌 已断开与004电脑的连接" "Yellow"
}

# 连接按钮事件
$connectButton.Add_Click({
    if ($global:connected) {
        Disconnect-From004
    } else {
        Connect-To004
    }
})

# 清空日志按钮事件
$clearButton.Add_Click({
    $outputBox.Clear()
    Write-Log "📝 日志已清空" "Gray"
})

Write-Log "🖥️ 002连接004控制程序已启动" "Green"
Write-Log "📋 请选择用户类型，然后点击'连接004电脑'开始" "Cyan"
Write-Log "🎯 目标: 004电脑 - **************" "Cyan"
Write-Log "👤 支持双用户: administrator (系统) / apple (桌面)" "Cyan"

# 显示窗体
$form.ShowDialog()
