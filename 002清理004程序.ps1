# 002清理004程序 - 专用清理工具
# 版本: v1.0 | 创建时间: 2025-07-23
# 功能: 002电脑清理004电脑
# 目标IP: ************** (已更新正确IP)
# 用户配置: administrator (系统) / apple (桌面)

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 全局变量
$global:session = $null
$global:connected = $false
$global:currentUser = "administrator"

# 创建主窗体
$form = New-Object System.Windows.Forms.Form
$form.Text = "002清理004程序 - 专用清理工具"
$form.Size = New-Object System.Drawing.Size(950, 750)
$form.StartPosition = "CenterScreen"
$form.BackColor = [System.Drawing.Color]::FromArgb(245, 248, 250)
$form.FormBorderStyle = "FixedSingle"
$form.MaximizeBox = $false

# 标题标签
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "🧹 002电脑 (PC-002) 清理004电脑 - 专用清理工具"
$titleLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 16, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
$titleLabel.Location = New-Object System.Drawing.Point(20, 20)
$titleLabel.Size = New-Object System.Drawing.Size(900, 40)
$titleLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($titleLabel)

# 用户选择面板
$userPanel = New-Object System.Windows.Forms.Panel
$userPanel.Location = New-Object System.Drawing.Point(20, 70)
$userPanel.Size = New-Object System.Drawing.Size(900, 50)
$userPanel.BackColor = [System.Drawing.Color]::FromArgb(255, 248, 220)
$userPanel.BorderStyle = "FixedSingle"
$form.Controls.Add($userPanel)

# 用户选择标签
$userLabel = New-Object System.Windows.Forms.Label
$userLabel.Text = "👤 选择004电脑用户:"
$userLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10, [System.Drawing.FontStyle]::Bold)
$userLabel.ForeColor = [System.Drawing.Color]::FromArgb(184, 134, 11)
$userLabel.Location = New-Object System.Drawing.Point(20, 15)
$userLabel.Size = New-Object System.Drawing.Size(150, 20)
$userPanel.Controls.Add($userLabel)

# 系统用户单选按钮
$adminRadio = New-Object System.Windows.Forms.RadioButton
$adminRadio.Text = "🔧 administrator (系统用户)"
$adminRadio.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$adminRadio.Location = New-Object System.Drawing.Point(180, 15)
$adminRadio.Size = New-Object System.Drawing.Size(200, 20)
$adminRadio.Checked = $true
$userPanel.Controls.Add($adminRadio)

# 桌面用户单选按钮
$appleRadio = New-Object System.Windows.Forms.RadioButton
$appleRadio.Text = "🍎 apple (桌面用户)"
$appleRadio.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$appleRadio.Location = New-Object System.Drawing.Point(400, 15)
$appleRadio.Size = New-Object System.Drawing.Size(180, 20)
$userPanel.Controls.Add($appleRadio)

# 双用户清理选项
$dualUserCheck = New-Object System.Windows.Forms.CheckBox
$dualUserCheck.Text = "🔄 双用户清理 (同时清理两个用户)"
$dualUserCheck.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$dualUserCheck.Location = New-Object System.Drawing.Point(600, 15)
$dualUserCheck.Size = New-Object System.Drawing.Size(250, 20)
$userPanel.Controls.Add($dualUserCheck)

# 连接状态面板
$statusPanel = New-Object System.Windows.Forms.Panel
$statusPanel.Location = New-Object System.Drawing.Point(20, 130)
$statusPanel.Size = New-Object System.Drawing.Size(900, 80)
$statusPanel.BackColor = [System.Drawing.Color]::FromArgb(248, 249, 250)
$statusPanel.BorderStyle = "FixedSingle"
$form.Controls.Add($statusPanel)

# 连接状态标签
$statusLabel = New-Object System.Windows.Forms.Label
$statusLabel.Text = "🔴 未连接到004电脑"
$statusLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 12, [System.Drawing.FontStyle]::Bold)
$statusLabel.ForeColor = [System.Drawing.Color]::Red
$statusLabel.Location = New-Object System.Drawing.Point(20, 15)
$statusLabel.Size = New-Object System.Drawing.Size(350, 25)
$statusPanel.Controls.Add($statusLabel)

# 004电脑信息标签
$infoLabel = New-Object System.Windows.Forms.Label
$infoLabel.Text = "目标: 004电脑 (**************) | 用户: administrator | 密码: (空)"
$infoLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$infoLabel.ForeColor = [System.Drawing.Color]::FromArgb(70, 70, 70)
$infoLabel.Location = New-Object System.Drawing.Point(20, 45)
$infoLabel.Size = New-Object System.Drawing.Size(650, 20)
$statusPanel.Controls.Add($infoLabel)

# 连接按钮
$connectButton = New-Object System.Windows.Forms.Button
$connectButton.Text = "🔗 连接004电脑"
$connectButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 11, [System.Drawing.FontStyle]::Bold)
$connectButton.Location = New-Object System.Drawing.Point(700, 20)
$connectButton.Size = New-Object System.Drawing.Size(150, 40)
$connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 123, 255)
$connectButton.ForeColor = [System.Drawing.Color]::White
$connectButton.FlatStyle = "Flat"
$statusPanel.Controls.Add($connectButton)

# 清理功能面板
$cleanPanel = New-Object System.Windows.Forms.Panel
$cleanPanel.Location = New-Object System.Drawing.Point(20, 230)
$cleanPanel.Size = New-Object System.Drawing.Size(900, 280)
$cleanPanel.BackColor = [System.Drawing.Color]::FromArgb(255, 248, 248)
$cleanPanel.BorderStyle = "FixedSingle"
$form.Controls.Add($cleanPanel)

# 清理功能标题
$cleanTitle = New-Object System.Windows.Forms.Label
$cleanTitle.Text = "🧹 004电脑清理功能 (支持双用户)"
$cleanTitle.Font = New-Object System.Drawing.Font("Microsoft YaHei", 14, [System.Drawing.FontStyle]::Bold)
$cleanTitle.ForeColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
$cleanTitle.Location = New-Object System.Drawing.Point(20, 10)
$cleanTitle.Size = New-Object System.Drawing.Size(350, 30)
$cleanPanel.Controls.Add($cleanTitle)

# 创建清理按钮
$cleanButtons = @(
    @{Text="🌐 浏览器缓存清理"; Command="BrowserClean"; X=20; Y=50; Width=200; Height=40},
    @{Text="💻 VSCode缓存清理"; Command="VSCodeClean"; X=240; Y=50; Width=200; Height=40},
    @{Text="🗂️ 系统临时文件清理"; Command="TempClean"; X=460; Y=50; Width=200; Height=40},
    @{Text="🔄 全面清理"; Command="FullClean"; X=680; Y=50; Width=180; Height=40},
    @{Text="📁 回收站清理"; Command="RecycleBin"; X=20; Y=110; Width=200; Height=40},
    @{Text="🌐 DNS缓存刷新"; Command="DNSFlush"; X=240; Y=110; Width=200; Height=40},
    @{Text="⚡ 进程优化"; Command="ProcessOpt"; X=460; Y=110; Width=200; Height=40},
    @{Text="📊 清理报告"; Command="CleanReport"; X=680; Y=110; Width=180; Height=40},
    @{Text="🎯 Augment修复"; Command="AugmentFix"; X=20; Y=170; Width=200; Height=40},
    @{Text="🚀 性能优化"; Command="Performance"; X=240; Y=170; Width=200; Height=40},
    @{Text="🔧 注册表清理"; Command="RegistryClean"; X=460; Y=170; Width=200; Height=40},
    @{Text="🍎 Apple用户清理"; Command="AppleClean"; X=680; Y=170; Width=180; Height=40},
    @{Text="👤 双用户清理"; Command="DualUserClean"; X=20; Y=230; Width=200; Height=40},
    @{Text="🔐 权限修复"; Command="PermissionFix"; X=240; Y=230; Width=200; Height=40},
    @{Text="📱 用户切换清理"; Command="UserSwitchClean"; X=460; Y=230; Width=200; Height=40},
    @{Text="🔄 重启004"; Command="Restart"; X=680; Y=230; Width=180; Height=40}
)

foreach ($btn in $cleanButtons) {
    $button = New-Object System.Windows.Forms.Button
    $button.Text = $btn.Text
    $button.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
    $button.Location = New-Object System.Drawing.Point($btn.X, $btn.Y)
    $button.Size = New-Object System.Drawing.Size($btn.Width, $btn.Height)
    $button.BackColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
    $button.ForeColor = [System.Drawing.Color]::White
    $button.FlatStyle = "Flat"
    $button.Enabled = $false
    $button.Tag = $btn.Command
    $cleanPanel.Controls.Add($button)
}

# 输出文本框
$outputBox = New-Object System.Windows.Forms.TextBox
$outputBox.Location = New-Object System.Drawing.Point(20, 540)
$outputBox.Size = New-Object System.Drawing.Size(900, 150)
$outputBox.Multiline = $true
$outputBox.ScrollBars = "Vertical"
$outputBox.Font = New-Object System.Drawing.Font("Consolas", 9)
$outputBox.BackColor = [System.Drawing.Color]::FromArgb(33, 37, 41)
$outputBox.ForeColor = [System.Drawing.Color]::FromArgb(248, 249, 250)
$outputBox.ReadOnly = $true
$form.Controls.Add($outputBox)

# 输出标签
$outputLabel = New-Object System.Windows.Forms.Label
$outputLabel.Text = "📝 清理日志"
$outputLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10, [System.Drawing.FontStyle]::Bold)
$outputLabel.ForeColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
$outputLabel.Location = New-Object System.Drawing.Point(20, 515)
$outputLabel.Size = New-Object System.Drawing.Size(100, 20)
$form.Controls.Add($outputLabel)

# 清空日志按钮
$clearButton = New-Object System.Windows.Forms.Button
$clearButton.Text = "🗑️ 清空日志"
$clearButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 8)
$clearButton.Location = New-Object System.Drawing.Point(830, 510)
$clearButton.Size = New-Object System.Drawing.Size(90, 25)
$clearButton.BackColor = [System.Drawing.Color]::FromArgb(108, 117, 125)
$clearButton.ForeColor = [System.Drawing.Color]::White
$clearButton.FlatStyle = "Flat"
$form.Controls.Add($clearButton)

# 进度条
$progressBar = New-Object System.Windows.Forms.ProgressBar
$progressBar.Location = New-Object System.Drawing.Point(20, 700)
$progressBar.Size = New-Object System.Drawing.Size(900, 20)
$progressBar.Style = "Continuous"
$form.Controls.Add($progressBar)

# 函数：输出日志
function Write-Log {
    param($message, $color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $logMessage = "[$timestamp] $message`r`n"
    $outputBox.AppendText($logMessage)
    $outputBox.SelectionStart = $outputBox.Text.Length
    $outputBox.ScrollToCaret()
    [System.Windows.Forms.Application]::DoEvents()
}

# 函数：更新进度条
function Update-Progress {
    param($value)
    $progressBar.Value = $value
    [System.Windows.Forms.Application]::DoEvents()
}

# 函数：更新用户信息
function Update-UserInfo {
    if ($adminRadio.Checked) {
        $global:currentUser = "administrator"
        $infoLabel.Text = "目标: 004电脑 (192.168.101.44) | 用户: administrator (系统) | 密码: (空)"
    } else {
        $global:currentUser = "apple"
        $infoLabel.Text = "目标: 004电脑 (192.168.101.44) | 用户: apple (桌面) | 密码: (空)"
    }
    
    if ($dualUserCheck.Checked) {
        $infoLabel.Text += " | 模式: 双用户清理"
    }
}

# 用户选择事件
$adminRadio.Add_CheckedChanged({ Update-UserInfo })
$appleRadio.Add_CheckedChanged({ Update-UserInfo })
$dualUserCheck.Add_CheckedChanged({ Update-UserInfo })

# 函数：连接004电脑
function Connect-To004 {
    Write-Log "正在连接到004电脑 (用户: $global:currentUser)..." "Yellow"
    Update-Progress 10
    
    try {
        # 配置WinRM
        Set-Item WSMan:\localhost\Client\TrustedHosts -Value '*' -Force
        Update-Progress 30
        
        # 测试连接
        if (-not (Test-Connection -ComputerName "**************" -Count 1 -Quiet)) {
            throw "网络连接失败 - 004电脑可能未开机或网络不通"
        }
        Update-Progress 50
        
        # 创建凭据
        $securePassword = New-Object System.Security.SecureString
        $cred = New-Object PSCredential($global:currentUser, $securePassword)
        
        # 建立会话
        $global:session = New-PSSession -ComputerName "**************" -Credential $cred
        Update-Progress 80
        
        # 获取004电脑信息
        $info = Invoke-Command -Session $global:session -ScriptBlock {
            @{
                Hostname = hostname
                User = whoami
                OS = (Get-WmiObject Win32_OperatingSystem).Caption
                FreeSpace = [math]::Round((Get-WmiObject Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB, 2)
                Users = (Get-WmiObject Win32_UserAccount -Filter "LocalAccount=True").Name -join ", "
                Desktop = [Environment]::GetFolderPath('Desktop')
            }
        }
        
        $global:connected = $true
        $statusLabel.Text = "🟢 已连接到004电脑 (用户: $global:currentUser)"
        $statusLabel.ForeColor = [System.Drawing.Color]::Green
        $connectButton.Text = "🔌 断开连接"
        $connectButton.BackColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
        
        # 启用清理按钮
        foreach ($control in $cleanPanel.Controls) {
            if ($control -is [System.Windows.Forms.Button] -and $control.Tag) {
                $control.Enabled = $true
            }
        }
        
        Update-Progress 100
        Write-Log "✅ 成功连接到004电脑" "Green"
        Write-Log "主机名: $($info.Hostname)" "Cyan"
        Write-Log "当前用户: $($info.User)" "Cyan"
        Write-Log "系统: $($info.OS)" "Cyan"
        Write-Log "C盘可用空间: $($info.FreeSpace) GB" "Cyan"
        Write-Log "本地用户: $($info.Users)" "Cyan"
        Write-Log "桌面路径: $($info.Desktop)" "Cyan"
        
        if ($dualUserCheck.Checked) {
            Write-Log "🔄 双用户清理模式已启用" "Yellow"
        }
        
        Start-Sleep -Seconds 1
        Update-Progress 0
        
    } catch {
        Update-Progress 0
        Write-Log "❌ 连接失败: $($_.Exception.Message)" "Red"
        [System.Windows.Forms.MessageBox]::Show("连接004电脑失败:`n$($_.Exception.Message)`n`n请确保:`n1. 004电脑已开机`n2. 004电脑网络正常`n3. 004电脑已启用远程管理`n4. 选择的用户账户正确", "连接错误", "OK", "Error")
    }
}

# 函数：断开连接
function Disconnect-From004 {
    if ($global:session) {
        Remove-PSSession $global:session
        $global:session = $null
    }
    
    $global:connected = $false
    $statusLabel.Text = "🔴 未连接到004电脑"
    $statusLabel.ForeColor = [System.Drawing.Color]::Red
    $connectButton.Text = "🔗 连接004电脑"
    $connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 123, 255)
    
    # 禁用清理按钮
    foreach ($control in $cleanPanel.Controls) {
        if ($control -is [System.Windows.Forms.Button] -and $control.Tag) {
            $control.Enabled = $false
        }
    }
    
    Write-Log "🔌 已断开与004电脑的连接" "Yellow"
}

# 连接按钮事件
$connectButton.Add_Click({
    if ($global:connected) {
        Disconnect-From004
    } else {
        Connect-To004
    }
})

# 清空日志按钮事件
$clearButton.Add_Click({
    $outputBox.Clear()
    Write-Log "📝 日志已清空" "Gray"
})

Write-Log "🧹 002清理004程序已启动" "Green"
Write-Log "📋 请选择用户类型，然后点击'连接004电脑'开始清理" "Cyan"
Write-Log "🎯 目标: 004电脑 - 192.168.101.44" "Cyan"
Write-Log "👤 支持双用户: administrator (系统) / apple (桌面)" "Cyan"
Write-Log "🔄 支持双用户清理模式" "Cyan"

# 显示窗体
$form.ShowDialog()
