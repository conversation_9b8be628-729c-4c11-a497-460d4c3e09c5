🖥️ 002连接004程序套件 - 完整使用说明
===========================================

📋 程序简介
-----------
这是一套专为002电脑设计的程序，用于远程连接和控制004电脑。
004电脑具有特殊的双用户配置，本程序完全支持这种配置。

🎯 目标配置
-----------
• 源电脑: 002电脑 (PC-002) - 运行本程序
• 目标电脑: 004电脑 (**************) - 被控制的电脑
• 系统用户: administrator (系统管理)
• 桌面用户: apple (日常使用)
• 密码: (两个用户都是空密码)
• 协议: PowerShell WinRM

📦 程序组件
-----------
1. 002连接004控制程序.ps1 - 主控制程序
2. 002清理004程序.ps1 - 专用清理程序
3. 启动002连接004程序.bat - 控制程序启动器
4. 启动002清理004程序.bat - 清理程序启动器
5. 002连接004程序使用说明.txt - 本说明文件

🚀 快速开始
-----------

方法一：使用控制程序
1. 双击运行 "启动002连接004程序.bat"
2. 选择用户类型 (administrator 或 apple)
3. 点击 "🔗 连接004电脑" 按钮
4. 连接成功后，使用各种控制功能

方法二：使用清理程序
1. 双击运行 "启动002清理004程序.bat"
2. 选择用户类型 (administrator 或 apple)
3. 可选择启用 "双用户清理" 模式
4. 点击 "🔗 连接004电脑" 按钮
5. 连接成功后，选择需要的清理功能

👤 用户类型说明
---------------

🔧 administrator (系统用户)
- 系统级管理权限
- 可以访问所有系统文件
- 适合系统维护和管理
- 可以修改系统设置

🍎 apple (桌面用户)
- 桌面用户权限
- 主要用于日常使用
- 适合清理用户文件
- 访问用户桌面和文档

🔄 双用户清理模式
- 同时清理两个用户的文件
- 一次性完成全面清理
- 提高清理效率
- 确保系统彻底优化

🎮 控制程序功能
---------------

📊 系统状态
- 查看004电脑的CPU、内存、磁盘使用情况
- 实时监控系统性能
- 显示系统运行状态

📁 文件管理
- 浏览004电脑的文件和文件夹
- 查看文件大小和修改时间
- 远程文件操作

🧹 清理缓存
- 清理004电脑的临时文件
- 清理浏览器缓存
- 释放磁盘空间

💻 VSCode修复
- 修复004电脑的VSCode问题
- 清理VSCode缓存
- 解决Augment插件问题

🔧 服务管理
- 查看004电脑的系统服务状态
- 管理重要服务
- 服务启动和停止

🌐 网络测试
- 测试004电脑的网络连接
- 检查网络状态
- 网络诊断

👤 用户管理
- 管理004电脑的用户账户
- 查看用户信息
- 用户权限管理

🔄 重启004
- 安全重启004电脑
- 需要确认操作
- 自动断开连接

📋 获取信息
- 获取004电脑的详细系统信息
- 硬件配置信息
- 软件版本信息

🍎 Apple桌面
- 访问apple用户的桌面
- 查看桌面文件
- 桌面管理

🔐 权限检查
- 检查当前用户权限
- 验证访问权限
- 权限诊断

📱 双用户状态
- 查看两个用户的状态
- 用户切换信息
- 双用户管理

🧹 清理程序功能
---------------

🌐 浏览器缓存清理
- 清理Chrome、Edge、360浏览器的缓存
- 支持两个用户的浏览器
- 自动停止浏览器进程
- 显示清理的具体大小

💻 VSCode缓存清理
- 清理VSCode的globalStorage和workspaceStorage
- 清理VSCode日志文件
- 清理OAuth认证缓存
- 支持两个用户的VSCode

🗂️ 系统临时文件清理
- 清理用户临时文件夹
- 清理系统临时文件夹
- 清理Windows临时文件
- 显示释放的磁盘空间

🔄 全面清理
- 执行所有清理任务
- 一键完成全面优化
- 最彻底的清理方案

📁 回收站清理
- 一键清空004电脑回收站
- 支持两个用户的回收站
- 彻底释放磁盘空间

🌐 DNS缓存刷新
- 刷新DNS解析缓存
- 提升网络连接速度
- 解决网络连接问题

⚡ 进程优化
- 停止不必要的浏览器进程
- 释放系统内存
- 提升系统性能

📊 清理报告
- 显示004电脑当前状态
- CPU、内存、磁盘使用情况
- 系统运行时间统计

🎯 Augment修复
- 专门修复VSCode Augment OAuth登录问题
- 清理认证相关缓存
- 重置浏览器指纹数据

🚀 性能优化
- 系统性能优化
- 内存清理
- 启动项优化

🔧 注册表清理
- 清理无效注册表项
- 优化系统注册表
- 提升系统性能

🍎 Apple用户清理
- 专门清理apple用户的文件
- 清理用户缓存
- 优化用户环境

👤 双用户清理
- 同时清理两个用户
- 一次性完成全面清理
- 提高清理效率

🔐 权限修复
- 修复文件权限问题
- 恢复用户权限
- 权限优化

📱 用户切换清理
- 清理用户切换缓存
- 优化用户切换性能
- 解决切换问题

⚠️ 注意事项
-----------
1. 确保004电脑已开机并连接到网络
2. 确保004电脑已启用远程管理功能
3. 根据需要选择正确的用户类型
4. 双用户清理模式需要更多时间
5. 重启功能需要谨慎使用

🔍 故障排除
-----------
问题：无法连接到004电脑
解决：
- 检查004电脑是否在线 (**************)
- 确认004电脑已启用WinRM服务
- 检查网络连接
- 验证用户账户是否正确

问题：用户权限不足
解决：
- 尝试使用administrator用户
- 检查用户账户状态
- 验证用户权限

问题：双用户清理失败
解决：
- 分别使用单用户模式清理
- 检查两个用户的状态
- 确保用户账户都可用

📝 使用建议
-----------
1. 建议每周执行一次全面清理
2. 系统管理使用administrator用户
3. 日常清理使用apple用户
4. 定期使用双用户清理模式
5. 清理前先查看系统状态

📞 技术支持
-----------
程序版本: v1.0
创建时间: 2025-07-23
适用环境: Windows 10/11
PowerShell版本: 5.1+

目标电脑: 004电脑
目标IP: **************
连接协议: PowerShell WinRM
支持用户: administrator / apple

📝 更新日志
-----------
v1.0 (2025-07-23)
- 初始版本发布
- 实现双用户支持
- 添加图形化操作界面
- 提供实时进度和日志显示
- 专业的Augment修复功能
- 针对004电脑双用户配置优化
- 支持双用户清理模式
