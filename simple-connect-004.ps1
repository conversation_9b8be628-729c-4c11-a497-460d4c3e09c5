# 简单连接004电脑脚本 - apple用户
Write-Host "正在连接004电脑 (apple用户)..." -ForegroundColor Green

# 配置参数
$targetIP = "**************"
$username = "apple"

# 创建空密码凭据 - 使用特殊方法
$securePassword = ConvertTo-SecureString "" -AsPlainText -Force
$cred = New-Object PSCredential($username, $securePassword)

# 配置WinRM信任主机
Set-Item WSMan:\localhost\Client\TrustedHosts -Value '*' -Force

try {
    # 建立会话
    Write-Host "正在建立PowerShell会话..." -ForegroundColor Yellow
    $session = New-PSSession -ComputerName $targetIP -Credential $cred
    
    # 获取004电脑信息
    Write-Host "正在获取004电脑信息..." -ForegroundColor Yellow
    $info = Invoke-Command -Session $session -ScriptBlock {
        @{
            Hostname = hostname
            User = whoami
            Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            OS = (Get-WmiObject Win32_OperatingSystem).Caption
            Desktop = [Environment]::GetFolderPath('Desktop')
        }
    }
    
    # 显示连接成功信息
    Write-Host ""
    Write-Host "✅ 成功连接到004电脑!" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host "主机名: $($info.Hostname)" -ForegroundColor White
    Write-Host "用户: $($info.User)" -ForegroundColor White
    Write-Host "时间: $($info.Time)" -ForegroundColor White
    Write-Host "系统: $($info.OS)" -ForegroundColor White
    Write-Host "桌面路径: $($info.Desktop)" -ForegroundColor White
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎉 连接成功！您现在可以控制004电脑了。" -ForegroundColor Yellow
    Write-Host "💡 会话已建立，可以使用 Invoke-Command 执行远程命令。" -ForegroundColor Cyan
    
    # 保存会话到全局变量
    $global:session004 = $session
    Write-Host "📝 会话已保存到 `$global:session004" -ForegroundColor Gray
    
} catch {
    Write-Host ""
    Write-Host "❌ 连接失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 故障排除建议:" -ForegroundColor Yellow
    Write-Host "1. 确保004电脑已开机" -ForegroundColor White
    Write-Host "2. 确保004电脑网络正常" -ForegroundColor White
    Write-Host "3. 确保004电脑已启用WinRM服务" -ForegroundColor White
    Write-Host "4. 确保apple用户账户存在且可用" -ForegroundColor White
}

Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
