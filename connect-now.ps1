Write-Host "正在连接004电脑 apple用户..." -ForegroundColor Green
$cred = New-Object PSCredential('apple', (ConvertTo-SecureString '' -AsPlainText -Force))
Set-Item WSMan:\localhost\Client\TrustedHosts -Value '*' -Force
$session = New-PSSession -ComputerName '**************' -Credential $cred
Write-Host "✅ 成功连接到004电脑!" -ForegroundColor Green
$info = Invoke-Command -Session $session -ScriptBlock { hostname; whoami; Get-Date }
Write-Host $info -ForegroundColor Cyan
Write-Host "🎉 连接成功！" -ForegroundColor Yellow
