﻿# 004 Computer Management Tools
Write-Host "004 Computer Management Tools" -ForegroundColor Green

if (-not $global:cred004) {
    Write-Host "Creating credentials..." -ForegroundColor Yellow
    $securePassword = New-Object System.Security.SecureString
    $global:cred004 = New-Object PSCredential("administrator", $securePassword)
    Set-Item WSMan:\localhost\Client\TrustedHosts -Value '*' -Force
}

function Get-004Info {
    Invoke-Command -ComputerName "**************" -Credential $global:cred004 -ScriptBlock {
        Get-ComputerInfo | Select WindowsProductName, TotalPhysicalMemory, CsProcessors
    }
}

function Get-004Processes {
    Invoke-Command -ComputerName "**************" -Credential $global:cred004 -ScriptBlock {
        Get-Process | Sort CPU -Desc | Select -First 5 Name, CPU, WorkingSet
    }
}

function Invoke-004Cmd {
    param([string]$Command)
    if (-not $Command) { $Command = Read-Host "Enter command" }
    Invoke-Command -ComputerName "**************" -Credential $global:cred004 -ScriptBlock {
        param($cmd)
        Invoke-Expression $cmd
    } -ArgumentList $Command
}

Write-Host "Available functions:" -ForegroundColor Yellow
Write-Host "  Get-004Info      - Get system info" -ForegroundColor White
Write-Host "  Get-004Processes - Get top processes" -ForegroundColor White
Write-Host "  Invoke-004Cmd    - Execute remote command" -ForegroundColor White
