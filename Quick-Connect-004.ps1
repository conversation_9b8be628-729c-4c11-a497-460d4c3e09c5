﻿# 002 Computer Quick Connect to 004 Computer
Write-Host "002 Computer Connecting to 004 Computer..." -ForegroundColor Green
$targetIP = "**************"
$username = "administrator"
$securePassword = New-Object System.Security.SecureString
$cred = New-Object PSCredential($username, $securePassword)
Set-Item WSMan:\localhost\Client\TrustedHosts -Value '*' -Force

try {
    $result = Invoke-Command -ComputerName $targetIP -Credential $cred -ScriptBlock {
        @{
            "Computer" = $env:COMPUTERNAME
            "User" = $env:USERNAME
            "Time" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            "IP" = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -like "192.168.*"}).IPAddress
        }
    }
    Write-Host "SUCCESS: Connected to 004 computer!" -ForegroundColor Green
    $result.GetEnumerator() | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value)" -ForegroundColor White
    }
    $global:cred004 = $cred
    Write-Host "Credentials saved to $global:cred004" -ForegroundColor Cyan
} catch {
    Write-Host "FAILED: Connection error: $($_.Exception.Message)" -ForegroundColor Red
}
