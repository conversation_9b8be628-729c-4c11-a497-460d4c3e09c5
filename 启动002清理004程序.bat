@echo off
title 002清理004程序 - 启动器
color 0C

echo.
echo ========================================
echo   002清理004程序 - 专用清理工具
echo ========================================
echo.
echo 正在启动清理程序...
echo.
echo 目标: 004电脑 (192.168.101.44)
echo 支持用户: administrator / apple
echo 特色: 双用户清理模式
echo.

REM 检查PowerShell执行策略
powershell -Command "if ((Get-ExecutionPolicy) -eq 'Restricted') { Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force }"

REM 启动PowerShell GUI清理程序
powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File "%~dp0002清理004程序.ps1"

echo.
echo 清理程序已启动！
echo 如果没有看到清理界面，请检查是否被防火墙阻止。
echo.
pause
