@echo off
title 002连接004控制程序 - 启动器
color 0A

echo.
echo ========================================
echo   002连接004控制程序 - 启动器
echo ========================================
echo.
echo 正在启动控制程序...
echo.
echo 目标: 004电脑 (192.168.101.44)
echo 支持用户: administrator / apple
echo.

REM 检查PowerShell执行策略
powershell -Command "if ((Get-ExecutionPolicy) -eq 'Restricted') { Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force }"

REM 启动PowerShell GUI程序
powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File "%~dp0002连接004控制程序.ps1"

echo.
echo 控制程序已启动！
echo 如果没有看到控制界面，请检查是否被防火墙阻止。
echo.
pause
