﻿<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <title>002鐢佃剳杩炴帴004鐢佃剳鏁欑▼</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        h1 { color: #2c3e50; text-align: center; border-bottom: 2px solid #3498db; }
        h2 { color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; }
        .info { background: #e8f4fd; border: 1px solid #3498db; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .warning { background: #fff3cd; border: 1px solid #ffc107; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #28a745; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .code { background: #f8f9fa; border: 1px solid #ddd; padding: 10px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background: #3498db; color: white; }
        .ai-note { background: #e1f5fe; border: 2px solid #0288d1; padding: 20px; margin: 20px 0; border-radius: 8px; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>002鐢佃剳杩炴帴004鐢佃剳瀹屾暣鏁欑▼</h1>
        
        <div class='ai-note'>
            <h3>馃 AI鍔╂墜涓撶敤鎸囧崡</h3>
            <p>鏈暀绋嬩笓涓篈I鍔╂墜璁捐锛屾彁渚涗粠002鐢佃剳杩炴帴鍒?04鐢佃剳鐨勫畬鏁存寚瀵笺€?/p>
        </div>

        <h2>馃搵 杩炴帴淇℃伅</h2>
        <table>
            <tr><th>椤圭洰</th><th>鍊?/th><th>璇存槑</th></tr>
            <tr><td>婧愮數鑴?/td><td>002</td><td>鍙戣捣杩炴帴鐨勭數鑴?/td></tr>
            <tr><td>鐩爣鐢佃剳</td><td>004</td><td>琚繛鎺ョ殑鐢佃剳</td></tr>
            <tr><td>鐩爣IP</td><td>**************</td><td>004鐢佃剳鐨処P鍦板潃</td></tr>
            <tr><td>鐢ㄦ埛鍚?/td><td>administrator</td><td>绠＄悊鍛樿处鎴?/td></tr>
            <tr><td>瀵嗙爜</td><td>(绌哄瘑鐮?</td><td>鏃犲瘑鐮?/td></tr>
            <tr><td>鍗忚</td><td>PowerShell WinRM</td><td>杩滅▼绠＄悊鍗忚</td></tr>
            <tr><td>绔彛</td><td>5985</td><td>WinRM榛樿绔彛</td></tr>
        </table>

        <h2>馃搧 鍙敤鏂囦欢</h2>
        <div class='info'>
            <h3>妗岄潰鏂囦欢澶? 002-Connect-004</h3>
            <ul>
                <li><strong>Connect-004.bat</strong> - 涓€閿繛鎺ユ壒澶勭悊鏂囦欢</li>
                <li><strong>Quick-Connect-004.ps1</strong> - 蹇€熻繛鎺ヨ剼鏈?/li>
                <li><strong>Manage-004.ps1</strong> - 绠＄悊宸ュ叿闆?/li>
                <li><strong>README.txt</strong> - 浣跨敤璇存槑</li>
                <li><strong>AI-README.md</strong> - AI鍔╂墜鎸囧崡</li>
                <li><strong>Tutorial.html</strong> - 鏈暀绋嬫枃浠?/li>
            </ul>
        </div>

        <h2>馃殌 杩炴帴鏂规硶</h2>
        
        <div class='success'>
            <h3>鏂规硶1: 涓€閿繛鎺?(鎺ㄨ崘)</h3>
            <p>鍙屽嚮 <code>Connect-004.bat</code> 鏂囦欢</p>
            <p>鉁?鏈€绠€鍗曠殑鏂规硶锛岄€傚悎鎵€鏈夌敤鎴?/p>
        </div>

        <div class='info'>
            <h3>鏂规硶2: PowerShell鑴氭湰</h3>
            <p>鍙抽敭 <code>Quick-Connect-004.ps1</code> 鈫?閫夋嫨 浣跨敤PowerShell杩愯</p>
        </div>

        <div class='code'>
            <h3>鏂规硶3: 鎵嬪姩鍛戒护</h3>
            <pre>
 = '**************'
 = New-Object PSCredential('administrator', (New-Object System.Security.SecureString))
Set-Item WSMan:\\localhost\\Client\\TrustedHosts -Value '*' -Force
Invoke-Command -ComputerName  -Credential  -ScriptBlock { hostname }
            </pre>
        </div>

        <h2>馃敡 绠＄悊鍔熻兘</h2>
        <div class='info'>
            <p>杩炴帴鎴愬姛鍚庯紝杩愯 <code>Manage-004.ps1</code> 鑾峰彇绠＄悊宸ュ叿锛?/p>
            <ul>
                <li><code>Get-004Info</code> - 鑾峰彇绯荤粺淇℃伅</li>
                <li><code>Get-004Processes</code> - 鏌ョ湅杩涚▼</li>
                <li><code>Invoke-004Cmd</code> - 鎵ц杩滅▼鍛戒护</li>
            </ul>
        </div>

        <h2>鈿狅笍 鏁呴殰鎺掗櫎</h2>
        <div class='warning'>
            <table>
                <tr><th>闂</th><th>瑙ｅ喅鏂规</th></tr>
                <tr><td>杩炴帴瓒呮椂</td><td>妫€鏌?04鐢佃剳鏄惁寮€鏈? ping **************</td></tr>
                <tr><td>WinRM閿欒</td><td>鍦?04鐢佃剳閰嶇疆杩滅▼鏈嶅姟</td></tr>
                <tr><td>璁よ瘉澶辫触</td><td>婵€娲籄dministrator璐︽埛</td></tr>
                <tr><td>闃茬伀澧欓樆姝?/td><td>閰嶇疆闃茬伀澧欏厑璁哥鍙?985</td></tr>
            </table>
        </div>

        <h2>馃搳 鎴愬姛鏍囧織</h2>
        <div class='success'>
            <p>杩炴帴鎴愬姛鍚庡簲璇ョ湅鍒帮細</p>
            <ul>
                <li>鉁?SUCCESS: Connected to 004 computer!</li>
                <li>鉁?鏄剧ず004鐢佃剳淇℃伅锛堜富鏈哄悕銆佺敤鎴枫€佹椂闂淬€両P锛?/li>
                <li>鉁?Credentials saved to global variable</li>
                <li>鉁?鑳藉鎵ц杩滅▼鍛戒护</li>
            </ul>
        </div>

        <h2>馃敀 瀹夊叏娉ㄦ剰</h2>
        <div class='warning'>
            <ul>
                <li>鈿狅笍 浠呴檺瀹夊叏鍐呯綉鐜浣跨敤</li>
                <li>鈿狅笍 绌哄瘑鐮佸瓨鍦ㄥ畨鍏ㄩ闄?/li>
                <li>鈿狅笍 杩炴帴鍚庡叿鏈夊畬鍏ㄧ鐞嗘潈闄?/li>
                <li>鈿狅笍 寤鸿瀹氭湡妫€鏌ュ畨鍏ㄩ厤缃?/li>
            </ul>
        </div>

        <hr>
        <p style='text-align: center; color: #666;'>
            鏂囨。鐗堟湰: v1.0 | 鍒涘缓鏃堕棿: 2025-07-23 | 閫傜敤: 002鐢佃剳杩炴帴004鐢佃剳
        </p>
    </div>
</body>
</html>
